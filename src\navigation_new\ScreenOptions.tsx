import React from 'react';
import {StyleSheet, TouchableOpacity} from 'react-native';
import {NativeStackNavigationOptions} from '@react-navigation/native-stack';
import {BackArrow} from '../assets/svgIcons';
import {Fonts, theme} from '../utilities/theme';

export const getScreenOptions = ({
  navigation,
}: any): NativeStackNavigationOptions => ({
  headerShown: false,
  headerShadowVisible: false,
  headerTitleAlign: 'center',
  headerLeft: () => (
    <TouchableOpacity
      style={{width: 30}}
      onPress={() => navigation.goBack()}
      hitSlop={styles.hitSlop}>
      <BackArrow stroke={theme.lightColors?.secondary} />
    </TouchableOpacity>
  ),
  headerTitleStyle: styles.headerTitleStyles,
  headerStyle: {
    backgroundColor: theme.lightColors?.background || theme.lightColors?.white,
  },
  navigationBarColor: theme.lightColors?.white,
});

const styles = StyleSheet.create({
  hitSlop: {
    left: 15,
    right: 15,
    bottom: 15,
    top: 15,
  },
  headerTitleStyles: {
    fontSize: 16,
    color: theme.lightColors?.black,
    fontFamily: Fonts.semiBold,
  },
});
