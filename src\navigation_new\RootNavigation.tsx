import React, {useEffect} from 'react';
import {NavigationContainer} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {useAppDispatch, useAppSelector} from '../store';
import Toast from 'react-native-toast-message';
import {loadLanguage} from '../store/LanguagesSlice';
import useFetchers from '../hooks/exec/useFetchers';
import {setStepCount} from '../store/userSlice';
import GoogleFit, {Scopes} from 'react-native-google-fit';
import {initializeSocket, disconnectSocket} from '../services/socketService';
import {RootStackParamList} from './NavigationTypes';
import {getScreenOptions} from './ScreenOptions';
import {getAuthScreens} from './AuthScreens';
import {getClientScreens} from './ClientScreens';
import {getCoachScreens} from './CoachScreens';

const RootStack = createNativeStackNavigator<RootStackParamList>();

const RootNavigation = () => {
  const user = useAppSelector(state => state.user.user);
  const {firstLaunch} = useAppSelector(state => state.user);
  const dispatch = useAppDispatch();
  useFetchers();

  useEffect(() => {
    if (user._id) {
      initializeSocket(user._id);
    }

    return () => {
      disconnectSocket();
    };
  }, [user._id]);

  useEffect(() => {
    dispatch(loadLanguage());
  }, []);

  const checkStep = async () => {
    await GoogleFit.checkIsAuthorized();
    console.log(GoogleFit.isAuthorized);

    const options = {
      scopes: [
        Scopes.FITNESS_ACTIVITY_READ,
        Scopes.FITNESS_ACTIVITY_WRITE,
        Scopes.FITNESS_BODY_READ,
        Scopes.FITNESS_BODY_WRITE,
      ],
    };
    GoogleFit.authorize(options)
      .then(authResult => {
        if (authResult.success) {
          console.log('AUTH_SUCCESS');
        } else {
          console.log('AUTH_DENIED', authResult.message);
        }
      })
      .catch(() => {
        console.log('AUTH_ERROR');
      });

    const res = await GoogleFit.getDailyStepCountSamples({});
    console.log(res);
    GoogleFit.startRecording(callback => {
      console.log(callback);
    });
  };

  // Determine initial route based on user state
  const getInitialRouteName = (): keyof RootStackParamList => {
    if (!user._id) {
      if (!firstLaunch) return 'Language';
      return 'SelectRole';
    }

    if (user.userType === 'client') {
      return 'ClientBottomTabNavigator';
    }

    return 'CoachBottomTabNavigator';
  };

  return (
    <NavigationContainer>
      <RootStack.Navigator
        initialRouteName={getInitialRouteName()}
        screenOptions={getScreenOptions}>
        {/* Auth Screens - Always available */}
        {getAuthScreens(firstLaunch)}

        {/* Client Screens - Only when user is client */}
        {user._id && user.userType === 'client' && getClientScreens()}

        {/* Coach Screens - Only when user is coach */}
        {user._id && user.userType === 'coach' && getCoachScreens()}
      </RootStack.Navigator>
      <Toast position="top" />
    </NavigationContainer>
  );
};

export default RootNavigation;
