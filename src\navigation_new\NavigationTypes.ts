import {Moment} from 'moment';
import {IFood} from '../interfaces/IFoods';
import {ICoachOfClient, IGoal, IUser} from '../interfaces/IUser.';
import {IDocument} from '../interfaces/IDocument';
import { BottomStackParamList } from '../navigation/BottomNavigation/BottomNavigation';


// Client Bottom Tab Navigator Param List
export type ClientBottomStackParamList = {
  Home: undefined;
  Profile: undefined;
  MealHistory: undefined;
  Chats: undefined;
  EditMeal: undefined;
  ChatDetails: {recipient?: ICoachOfClient; chatId?: string};
  PersonalInfo: undefined;
  MyGoals: undefined;
  EditGoals: undefined;
  Document: {isClient?: boolean};
  AddDocument: undefined;
  EditDocument: undefined;
  DocumentDetail: {document: IDocument};
  WeightProgress: {isClient?: boolean};
  CoachProfile: undefined;
  Language2: {isClient?: boolean};
  PreviousPassword: undefined;
  ResetPassword: undefined;
  PasswordFeedback: undefined;
  TermsAndCondition: undefined;
  HelpCenter: undefined;
  ClientAbout: undefined;
  Activity: undefined;
  AddExercise: undefined;
  ScanCode: undefined;
};

// Combined param list for all screens across the app
export type RootStackParamList = {
  // Auth Flow Screens
  Language: undefined;
  Onboard: undefined;
  Login: {isClient?: boolean};
  SignUp: {isClient?: boolean};
  SelectRole: undefined;
  EmailVerification: undefined;
  ResetPassword: {
    email?: string;
    otp?: string;
    previousPassword?: string;
    reset_channel: 'forgotPassword' | 'changePassword';
  };
  OTPVerification: {email?: string};
  PasswordFeedback: undefined;
  ProfileSetup: undefined;
  AddFirstClient: undefined;
  Subscription: undefined;
  AuthAddNewCard: undefined;
  PaymentFeedback: undefined;
  Question1: undefined;
  Question2: undefined;
  Question3: undefined;
  Question4: undefined;
  Question5: undefined;
  Question6: undefined;
  Question7: undefined;
  PreviousPassword: undefined;

  // Bottom Tab Navigators
  ClientBottomTabNavigator: ClientBottomStackParamList;
  CoachBottomTabNavigator: BottomStackParamList;

  // Common Screens
  Notifications: undefined;
  Document: {isClient?: boolean};
  AddDocument: undefined;
  EditDocument: {document?: IDocument};
  DocumentDetail: {document?: IDocument};
  TermsAndCondition: undefined;
  About: undefined;
  ClientAbout: undefined;
  HelpCenter: undefined;
  Language2: {isClient?: boolean};
  PersonalInfo: undefined;
  ViewSubscription: {isProfile: boolean};
  AddNewCard: {isProfile: boolean};
  WeightProgress: {isClient?: boolean};
  ChatDetails: {recipient?: ICoachOfClient | IUser; chatId?: string};

  // Client Specific Screens
  MealHistory: {
    mealId?: string;
    mealTitle: 'Breakfast' | 'Lunch' | 'Dinner' | 'Snacks';
    date: Moment;
  };
  SearchFood: {
    mealId?: string;
    mealTitle: 'Breakfast' | 'Lunch' | 'Dinner' | 'Snacks';
    mealFoods?: IFood[] | undefined;
  };
  EditMeal: undefined;
  MyGoals: undefined;
  EditGoals: {goal?: IGoal; clientId?: string; currentGoal?: IGoal};
  CoachProfile: undefined;
  Activity: undefined;
  AddExercise: undefined;
  ScanCode: {mealId: string | undefined};
  FoodDetails: {food: IFood};
  ClientProfile: undefined;
  Profile: undefined;

  // Coach Specific Screens
  Archive: undefined;
  ShareBarcode: undefined;
  ClientDetail: {details: IUser};
  ClientHistory: {clientId: string};
  AddClient: undefined;
  Subscription2: {isProfile: boolean};
};