import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {
  Language,
  Login,
  Onboard,
  SignUp,
  SelectRole,
  EmailVerification,
  ResetPassword,
  OTPVerification,
  PasswordFeedback,
  ProfileSetup,
  AddFirstClient,
  Subscription,
  AddNewCard,
  PaymentFeedback,
  PreviousPassword,
} from '../screens/authflow';
import Question1 from '../screens/authflow/ClientQuestionare/Question1';
import Question2 from '../screens/authflow/ClientQuestionare/Question2';
import Question3 from '../screens/authflow/ClientQuestionare/Question3';
import Question4 from '../screens/authflow/ClientQuestionare/Question4';
import Question5 from '../screens/authflow/ClientQuestionare/Question5';
import Question6 from '../screens/authflow/ClientQuestionare/Question6';
import Question7 from '../screens/authflow/ClientQuestionare/Question7';
import {RootStackParamList} from './NavigationTypes';
import {useTranslation} from 'react-i18next';

const Stack = createNativeStackNavigator<RootStackParamList>();

export const getAuthScreens = (firstLaunch: boolean) => {
  const {t} = useTranslation();

  return (
    <>
      {/* Pre-auth screens (only show if first launch) */}
      {!firstLaunch && <Stack.Screen name="Language" component={Language} />}
      {!firstLaunch && (
        <Stack.Screen
          name="Onboard"
          component={Onboard}
          options={{
            headerShown: true,
            headerTitle: '',
          }}
        />
      )}

      {/* Main auth screens */}
      <Stack.Screen name="SelectRole" component={SelectRole} />
      <Stack.Screen
        name="Login"
        component={Login}
        options={{headerShown: true, headerTitle: t('Login')}}
      />
      <Stack.Screen
        name="SignUp"
        component={SignUp}
        options={{headerShown: true, headerTitle: t('Sign Up')}}
      />
      <Stack.Screen
        name="EmailVerification"
        component={EmailVerification}
        options={{headerShown: true, headerTitle: t('Forgot Password')}}
      />
      <Stack.Screen
        name="ResetPassword"
        component={ResetPassword}
        options={{headerShown: true, headerTitle: t('Reset Password')}}
      />
      <Stack.Screen
        name="OTPVerification"
        component={OTPVerification}
        options={{headerShown: true, headerTitle: t('Forgot Password')}}
      />
      <Stack.Screen name="PasswordFeedback" component={PasswordFeedback} />
      <Stack.Screen name="ProfileSetup" component={ProfileSetup} />
      <Stack.Screen
        name="AddFirstClient"
        component={AddFirstClient}
        options={{headerShown: true, headerTitle: ''}}
      />
      <Stack.Screen name="Subscription" component={Subscription} />
      <Stack.Screen
        name="AuthAddNewCard"
        component={AddNewCard}
        options={{headerShown: true, headerTitle: 'Payment'}}
      />
      <Stack.Screen name="PaymentFeedback" component={PaymentFeedback} />
      <Stack.Screen
        name="PreviousPassword"
        component={PreviousPassword}
        options={{
          headerShown: true,
          headerTitle: t('Change Password'),
        }}
      />

      {/* Client questionnaire screens */}
      <Stack.Screen
        name="Question1"
        component={Question1}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="Question2"
        component={Question2}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="Question3"
        component={Question3}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="Question4"
        component={Question4}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="Question5"
        component={Question5}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="Question6"
        component={Question6}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="Question7"
        component={Question7}
        options={{headerShown: false}}
      />
    </>
  );
};
