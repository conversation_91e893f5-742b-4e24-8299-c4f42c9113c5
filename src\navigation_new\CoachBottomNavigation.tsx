import React from 'react';
import {createBottomTabNavigator} from '@react-navigation/bottom-tabs';
import {Image, Platform, StyleSheet, View} from 'react-native';
import PNGIcons from '../assets/pngIcons';
import {Fonts, theme} from '../utilities/theme';
import {Home, Clients, Chats, Profile} from '../screens/homeflow';
import {useAppSelector} from '../store';
import {Text} from 'react-native';
import {useTranslation} from 'react-i18next';
import {CoachBottomStackParamList} from './NavigationTypes';

const CoachBottomStack = createBottomTabNavigator<CoachBottomStackParamList>();

const CoachBottomNavigation = () => {
  const {t} = useTranslation();
  const unreadCounter = useAppSelector(state => state.chats.unreadCounter);

  const renderTabBarIcon = (iconSource: any, color: string) => (
    <Image
      resizeMode="contain"
      source={iconSource}
      style={[
        styles.tabBarIcon,
        {
          tintColor: color,
        },
      ]}
    />
  );

  const renderTabBarLabel = (
    label: string,
    focused: boolean,
    color: string,
  ) => (
    <Text
      style={[
        styles.tabBarLabel,
        {
          color: color,
          fontFamily: focused ? Fonts.semiBold : Fonts.regular,
        },
      ]}>
      {label}
    </Text>
  );

  return (
    <CoachBottomStack.Navigator
      screenOptions={({route}) => ({
        tabBarIcon: ({color}) => {
          let iconSource;
          if (route.name === 'Home') {
            iconSource = PNGIcons.HomeIcon;
          } else if (route.name === 'Clients') {
            iconSource = PNGIcons.SolarUser;
          } else if (route.name === 'Chats') {
            iconSource = PNGIcons.Message;
          } else if (route.name === 'Profile') {
            iconSource = PNGIcons.SolarUser2;
          }
          return renderTabBarIcon(iconSource, color);
        },
        tabBarLabel: ({focused, color}) => {
          let label = '';
          if (route.name === 'Home') {
            label = t('Home');
          } else if (route.name === 'Clients') {
            label = t('Clients');
          } else if (route.name === 'Chats') {
            label = t('Message');
          } else if (route.name === 'Profile') {
            label = t('Profile');
          }
          return renderTabBarLabel(label, focused, color);
        },
        tabBarActiveTintColor: theme.lightColors?.white,
        tabBarInactiveTintColor: `${theme.lightColors?.white}90`,
        headerShown: false,
        tabBarStyle: styles.tabBarStyle,
        tabBarHideOnKeyboard: true,
      })}>
      <CoachBottomStack.Screen
        name="Home"
        component={Home}
        options={{
          tabBarLabel: ({focused, color}) =>
            renderTabBarLabel(t('Home'), focused, color),
        }}
      />
      <CoachBottomStack.Screen
        name="Clients"
        component={Clients}
        options={{
          tabBarLabel: ({focused, color}) =>
            renderTabBarLabel(t('Clients'), focused, color),
        }}
      />

      <CoachBottomStack.Screen
        name="Chats"
        component={Chats}
        options={{
          tabBarIcon: ({color}) => (
            <View style={{position: 'relative'}}>
              {unreadCounter ? (
                <View style={styles.unreadContainer}>
                  <Text style={styles.unreadText}>{unreadCounter}</Text>
                </View>
              ) : null}

              <Image
                resizeMode="contain"
                source={PNGIcons.Message}
                style={[
                  styles.messageIcon,
                  {
                    tintColor: color,
                  },
                ]}
              />
            </View>
          ),

          tabBarLabel: ({focused, color}) =>
            renderTabBarLabel(t('Message'), focused, color),
        }}
      />
      <CoachBottomStack.Screen
        name="Profile"
        component={Profile}
        options={{
          tabBarLabel: ({focused, color}) =>
            renderTabBarLabel(t('Profile'), focused, color),
        }}
      />
    </CoachBottomStack.Navigator>
  );
};

const styles = StyleSheet.create({
  tabBarStyle: {
    backgroundColor: theme.lightColors?.primary,
    height: Platform.OS === 'ios' ? 85 : 70,
    paddingBottom: 19,
  },
  tabBarIcon: {
    width: 20,
    height: 20,
  },
  messageIcon: {
    width: 20,
    height: 20,
  },
  tabBarLabel: {
    fontSize: 12,
    marginTop: 5,
  },
  unreadContainer: {
    position: 'absolute',
    top: -5,
    right: -7,
    backgroundColor: 'red',
    paddingHorizontal: 5,
    paddingVertical: 2,
    borderRadius: 100,
    zIndex: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  unreadText: {
    fontSize: 8,
    color: 'white',
  },
});

export default CoachBottomNavigation;
