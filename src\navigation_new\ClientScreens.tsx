import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {
  About,
  Notifications,
  TermsAndCondition,
  HelpCenter,
  Document,
  AddDocument,
  EditDocument,
  DocumentDetail,
  WeightProgress,
  ChatDetails,
  ViewSubscription,
} from '../screens/homeflow';
import ClientBottomNavigation from './ClientBottomNavigation';
import MealHistory from '../screens/clientflow/MealHistory';
import EditMeal from '../screens/clientflow/EditMeal';
import ClientPersonalInfo from '../screens/clientflow/PersonalInformation';
import MyGoals from '../screens/clientflow/MyGoals';
import EditGoals from '../screens/clientflow/EditGoals';
import CoachProfile from '../screens/clientflow/CoachProfile';
import Activity from '../screens/clientflow/Activity';
import AddExercise from '../screens/clientflow/AddExercise';
import ScanCode from '../screens/clientflow/ScanCode';
import FoodDetails from '../screens/clientflow/FoodDetails/FoodDetails';
import SearchFood from '../screens/clientflow/SearchFood/SearchFood';
import {Language, AddNewCard} from '../screens/authflow';
import {RootStackParamList} from './NavigationTypes';
import {useTranslation} from 'react-i18next';

const Stack = createNativeStackNavigator<RootStackParamList>();

export const getClientScreens = () => {
  const {t} = useTranslation();

  return (
    <>
      {/* Main client bottom tab navigator */}
      <Stack.Screen
        name="ClientBottomTabNavigator"
        component={ClientBottomNavigation}
      />

      {/* Client-specific screens */}
      <Stack.Screen
        name="Notifications"
        component={Notifications}
        options={{
          headerShown: true,
          headerTitle: 'Notifications',
        }}
      />
      <Stack.Screen
        name="MealHistory"
        component={MealHistory}
        options={({route}: any) => ({
          headerShown: true,
          headerTitle: t(route.params.mealTitle),
        })}
      />
      <Stack.Screen
        name="SearchFood"
        component={SearchFood}
        options={{
          headerShown: true,
          headerTitle: t('Search Food'),
        }}
      />
      <Stack.Screen
        name="EditMeal"
        component={EditMeal}
        options={{
          headerShown: true,
          headerTitle: 'Wheat Atta Chapati',
        }}
      />
      <Stack.Screen
        name="ViewSubscription"
        component={ViewSubscription}
        options={{headerShown: true, headerTitle: t('Subscription')}}
      />
      <Stack.Screen
        name="ChatDetails"
        component={ChatDetails}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="PersonalInfo"
        component={ClientPersonalInfo}
        options={{headerShown: true, headerTitle: t('Personal Info')}}
      />
      <Stack.Screen
        name="MyGoals"
        component={MyGoals}
        options={{headerShown: true, headerTitle: t('My Goals')}}
      />
      <Stack.Screen
        name="EditGoals"
        component={EditGoals}
        options={{headerShown: true, headerTitle: t('Personal Info')}}
      />
      <Stack.Screen
        name="Document"
        component={Document}
        options={{headerShown: true, headerTitle: t('Document')}}
      />
      <Stack.Screen
        name="AddDocument"
        component={AddDocument}
        options={{headerShown: true, headerTitle: t('Add New Document')}}
      />
      <Stack.Screen
        name="EditDocument"
        component={EditDocument}
        options={{headerShown: true, headerTitle: t('Add New Document')}}
      />
      <Stack.Screen
        name="DocumentDetail"
        component={DocumentDetail}
        options={{headerShown: true, headerTitle: t('Details')}}
      />
      <Stack.Screen
        name="WeightProgress"
        component={WeightProgress}
        options={{headerShown: true, headerTitle: t('Weight Progress')}}
      />
      <Stack.Screen
        name="CoachProfile"
        component={CoachProfile}
        options={{headerShown: true, headerTitle: ''}}
      />
      <Stack.Screen
        name="Language2"
        component={Language}
        options={{
          headerShown: true,
          headerTitle: t('Select Language'),
        }}
      />
      <Stack.Screen
        name="TermsAndCondition"
        component={TermsAndCondition}
        options={{headerShown: true, headerTitle: t('Terms & Conditions')}}
      />
      <Stack.Screen
        name="HelpCenter"
        component={HelpCenter}
        options={{headerShown: true, headerTitle: t('Help Center')}}
      />
      <Stack.Screen
        name="ClientAbout"
        component={About}
        options={{headerShown: true, headerTitle: t('About')}}
      />
      <Stack.Screen
        name="Activity"
        component={Activity}
        options={{headerShown: true, headerTitle: t('Activity')}}
      />
      <Stack.Screen
        name="AddExercise"
        component={AddExercise}
        options={{headerShown: true, headerTitle: t('Add Exercise')}}
      />
      <Stack.Screen
        name="ScanCode"
        component={ScanCode}
        options={{headerShown: false, headerTitle: t('Scan Code')}}
      />
      <Stack.Screen
        name="FoodDetails"
        component={FoodDetails}
        options={({route}: any) => ({
          headerShown: true,
          headerTitle: route.params.food.name,
        })}
      />
      <Stack.Screen
        name="AddNewCard"
        component={AddNewCard}
        options={{headerShown: true, headerTitle: t('Payment')}}
      />
    </>
  );
};
