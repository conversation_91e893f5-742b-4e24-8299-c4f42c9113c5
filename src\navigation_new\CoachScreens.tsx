import React from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import {
  Notifications,
  Archive,
  TermsAndCondition,
  About,
  Document,
  AddDocument,
  EditDocument,
  ViewSubscription,
  HelpCenter,
  ShareBarcode,
  ClientDetail,
  ClientHistory,
  ChatDetails,
  EditGoals,
  WeightProgress,
  AddClient,
  DocumentDetail,
} from '../screens/homeflow';
import BottomNavigation from '../navigation/BottomNavigation/BottomNavigation';
import {
  AddNewCard,
  Language,
  PaymentFeedback,
  Subscription,
} from '../screens/authflow';
import PersonalInfo from '../screens/homeflow/PersonalInformation';
import {RootStackParamList} from './NavigationTypes';
import {useTranslation} from 'react-i18next';

const Stack = createNativeStackNavigator<RootStackParamList>();

export const getCoachScreens = () => {
  const {t} = useTranslation();

  return (
    <>
      {/* Main coach bottom tab navigator */}
      <Stack.Screen
        name="CoachBottomTabNavigator"
        component={BottomNavigation}
      />

      {/* Coach-specific screens */}
      <Stack.Screen
        name="Notifications"
        component={Notifications}
        options={{
          headerShown: true,
          headerTitle: t('Notifications'),
        }}
      />
      <Stack.Screen
        name="Archive"
        component={Archive}
        options={{
          headerShown: true,
          headerTitle: t('Archive'),
        }}
      />
      <Stack.Screen
        name="Language2"
        component={Language}
        options={{
          headerShown: true,
          headerTitle: t('Select Language'),
        }}
      />
      <Stack.Screen
        name="TermsAndCondition"
        component={TermsAndCondition}
        options={{headerShown: true, headerTitle: t('Terms & Conditions')}}
      />
      <Stack.Screen
        name="About"
        component={About}
        options={{headerShown: true, headerTitle: t('About')}}
      />
      <Stack.Screen
        name="PersonalInfo"
        component={PersonalInfo}
        options={{headerShown: true, headerTitle: t('Personal Info')}}
      />
      <Stack.Screen
        name="ViewSubscription"
        component={ViewSubscription}
        options={{headerShown: true, headerTitle: t('Subscription')}}
      />
      <Stack.Screen
        name="Subscription2"
        component={Subscription}
        options={{headerShown: true, headerTitle: t('Subscription')}}
      />
      <Stack.Screen
        name="AddNewCard"
        component={AddNewCard}
        options={{headerShown: true, headerTitle: t('Payment')}}
      />
      <Stack.Screen
        name="Document"
        component={Document}
        options={{headerShown: true, headerTitle: t('Document')}}
      />
      <Stack.Screen
        name="AddDocument"
        component={AddDocument}
        options={{headerShown: true, headerTitle: t('Add New Document')}}
      />
      <Stack.Screen
        name="EditDocument"
        component={EditDocument}
        options={{headerShown: true, headerTitle: t('Edit Document')}}
      />
      <Stack.Screen
        name="HelpCenter"
        component={HelpCenter}
        options={{headerShown: true, headerTitle: t('Help Center')}}
      />
      <Stack.Screen
        name="ShareBarcode"
        component={ShareBarcode}
        options={{headerShown: true, headerTitle: t('My QR')}}
      />
      <Stack.Screen
        name="ClientDetail"
        component={ClientDetail}
        options={{headerShown: true, headerTitle: ''}}
      />
      <Stack.Screen
        name="ClientHistory"
        component={ClientHistory}
        options={{headerShown: true, headerTitle: t('History')}}
      />
      <Stack.Screen
        name="ChatDetails"
        component={ChatDetails}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="EditGoals"
        component={EditGoals}
        options={{headerShown: true, headerTitle: t('Daily Goals')}}
      />
      <Stack.Screen
        name="WeightProgress"
        component={WeightProgress}
        options={{headerShown: true, headerTitle: t('Weight Progress')}}
      />
      <Stack.Screen
        name="AddClient"
        component={AddClient}
        options={{headerShown: true, headerTitle: t('Add Client')}}
      />
      <Stack.Screen
        name="DocumentDetail"
        component={DocumentDetail}
        options={{headerShown: true, headerTitle: t('Details')}}
      />
      <Stack.Screen name="PaymentFeedback" component={PaymentFeedback} />
    </>
  );
};
